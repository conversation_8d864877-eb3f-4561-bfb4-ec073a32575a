import React, { Suspense } from 'react';
import type { Metadata } from 'next';
import Container from '@/components/atoms/Container/Container';
import BillingHistoryClient from '@/components/organisms/BillingHistoryClient';
import {
  CreditCard,
  Clock,
  Download,
  Calendar,
  DollarSign,
  CheckCircle,
  XCircle,
  AlertCircle,
  MoreHorizontal,
  Receipt,
  FileText,
  TrendingUp
} from 'lucide-react';

// Page metadata
export const metadata: Metadata = {
  title: 'Billing History | EduSG',
  description: 'View your billing history and transaction details for your EduSG subscription',
};

// Loading component for Suspense fallback with improved design
function BillingHistoryLoading() {
  return (
    <Container variant="default" className="px-4 py-8">
      <div className="space-y-8">
        {/* Header skeleton with stats */}
        <div className="flex items-center justify-between">
          <div className="space-y-3">
            <div className="skeleton h-10 w-64"></div>
            <div className="skeleton h-5 w-80"></div>
          </div>
          <div className="flex gap-3">
            <div className="skeleton h-10 w-32"></div>
            <div className="skeleton h-10 w-24"></div>
          </div>
        </div>

        {/* Stats cards skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-3">
                  <div className="skeleton h-4 w-24"></div>
                  <div className="skeleton h-8 w-20"></div>
                </div>
                <div className="skeleton w-12 h-12 rounded-xl"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Filters skeleton */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="skeleton h-10 w-64"></div>
            <div className="skeleton h-10 w-48"></div>
            <div className="skeleton h-10 w-32"></div>
          </div>
        </div>

        {/* Table skeleton */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-6 border-b border-gray-100">
            <div className="skeleton h-6 w-48"></div>
          </div>
          <div className="divide-y divide-gray-100">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="p-6 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="skeleton w-12 h-12 rounded-xl"></div>
                  <div className="space-y-2">
                    <div className="skeleton h-5 w-48"></div>
                    <div className="skeleton h-4 w-32"></div>
                  </div>
                </div>
                <div className="text-right space-y-2">
                  <div className="skeleton h-5 w-20"></div>
                  <div className="skeleton h-4 w-16"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Container>
  );
}

// Sample billing data - to be replaced with real data from server
const sampleBillingHistory = [
  {
    id: 'inv_1234567890',
    date: '2024-01-15T10:30:00Z',
    description: 'EduSG Pro Plan - Monthly Subscription',
    amount: 2900, // cents
    currency: 'USD',
    status: 'paid' as const,
    paymentMethod: 'Visa •••• 4242',
    invoiceUrl: '#',
    receiptUrl: '#'
  },
  {
    id: 'inv_0987654321',
    date: '2023-12-15T10:30:00Z',
    description: 'EduSG Pro Plan - Monthly Subscription',
    amount: 2900,
    currency: 'USD',
    status: 'paid' as const,
    paymentMethod: 'Visa •••• 4242',
    invoiceUrl: '#',
    receiptUrl: '#'
  },
  {
    id: 'inv_1122334455',
    date: '2023-11-15T10:30:00Z',
    description: 'EduSG Basic Plan - Monthly Subscription',
    amount: 1500,
    currency: 'USD',
    status: 'paid' as const,
    paymentMethod: 'MasterCard •••• 5555',
    invoiceUrl: '#',
    receiptUrl: '#'
  },
  {
    id: 'inv_5544332211',
    date: '2023-10-15T10:30:00Z',
    description: 'EduSG Setup Fee',
    amount: 4900,
    currency: 'USD',
    status: 'failed' as const,
    paymentMethod: 'Visa •••• 4242',
    invoiceUrl: '#',
    receiptUrl: '#'
  }
];

// Status component
function StatusBadge({ status }: { status: string }) {
  const statusConfig = {
    paid: {
      icon: CheckCircle,
      className: 'bg-green-50 text-green-700 border-green-200',
      text: 'Paid'
    },
    failed: {
      icon: XCircle,
      className: 'bg-red-50 text-red-700 border-red-200',
      text: 'Failed'
    },
    pending: {
      icon: AlertCircle,
      className: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      text: 'Pending'
    }
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border ${config.className}`}>
      <Icon className="w-3 h-3" />
      {config.text}
    </span>
  );
}

// Main billing history content component
async function BillingHistoryContent() {
  // Fetch initial transaction data
  // For now, using sample data - replace with real server action call
  const initialTransactions = sampleBillingHistory;

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100);
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(new Date(dateString));
  };

  // Calculate stats based on sample data
  const totalSpent = sampleBillingHistory
    .filter(item => item.status === 'paid')
    .reduce((sum, item) => sum + item.amount, 0);
  
  const thisMonthSpent = sampleBillingHistory
    .filter(item => {
      const itemDate = new Date(item.date);
      const now = new Date();
      return item.status === 'paid' && 
             itemDate.getMonth() === now.getMonth() && 
             itemDate.getFullYear() === now.getFullYear();
    })
    .reduce((sum, item) => sum + item.amount, 0);

  return (
    <Container variant="default" className="px-4 py-8">
      <div className="space-y-8">
        {/* Header Section */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="space-y-2">
            <h1 className="text-3xl md:text-4xl font-bold text-text-primary flex items-center gap-3">
              <div className="w-12 h-12 bg-primary-action/10 rounded-xl flex items-center justify-center">
                <CreditCard className="w-6 h-6 text-primary-action" />
              </div>
              Billing History
            </h1>
            <p className="text-text-secondary text-lg">
              Track your payments, download invoices, and manage your billing preferences
            </p>
          </div>
          
          {/* Action buttons */}
          <div className="flex flex-wrap gap-3">
            <button className="btn btn-outline btn-sm gap-2">
              <Download className="w-4 h-4" />
              Export All
            </button>
            <button className="btn btn-primary btn-sm gap-2">
              <Receipt className="w-4 h-4" />
              Billing Portal
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Total Spent */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-text-secondary text-sm font-medium">Total Spent</p>
                <p className="text-2xl font-bold text-text-primary">
                  {formatAmount(totalSpent, 'USD')}
                </p>
                <p className="text-xs text-green-600 flex items-center gap-1">
                  <TrendingUp className="w-3 h-3" />
                  All time
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          {/* This Month */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-text-secondary text-sm font-medium">This Month</p>
                <p className="text-2xl font-bold text-text-primary">
                  {formatAmount(thisMonthSpent, 'USD')}
                </p>
                <p className="text-xs text-text-secondary">
                  Current billing cycle
                </p>
              </div>
              <div className="w-12 h-12 bg-green-50 rounded-xl flex items-center justify-center">
                <Calendar className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>

          {/* Success Rate */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-text-secondary text-sm font-medium">Success Rate</p>
                <p className="text-2xl font-bold text-text-primary">
                  {Math.round((sampleBillingHistory.filter(i => i.status === 'paid').length / sampleBillingHistory.length) * 100)}%
                </p>
                <p className="text-xs text-text-secondary">
                  Payment success
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-50 rounded-xl flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Dynamic Filters and Transactions - Wrapped in Client Component */}
        <BillingHistoryClient initialTransactions={initialTransactions}>
          {({ filteredTransactions, isPending, TransactionFilterComponent }) => (
            <>
              {/* Transaction Filter Form */}
              {TransactionFilterComponent}

              {/* Transactions Table */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <div className="p-6 border-b border-gray-100">
                  <h2 className="text-xl font-semibold text-text-primary flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Transaction History
                    {isPending && (
                      <span className="loading loading-spinner loading-sm text-primary-action"></span>
                    )}
                  </h2>
                </div>

                {filteredTransactions.length === 0 ? (
                  <div className="text-center py-16">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Clock className="w-8 h-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-text-primary mb-2">No transactions found</h3>
                    <p className="text-text-secondary mb-6">
                      {isPending 
                        ? "Loading transactions..." 
                        : "No transactions match your current filters."
                      }
                    </p>
                    <button className="btn btn-primary gap-2">
                      <CreditCard className="w-4 h-4" />
                      View Pricing Plans
                    </button>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-100">
                    {filteredTransactions.map((transaction) => (
                      <div key={transaction.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                        <div className="flex items-center justify-between">
                          {/* Left side - Transaction info */}
                          <div className="flex items-center space-x-4 flex-1">
                            <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                              transaction.status === 'paid'
                                ? 'bg-green-50'
                                : transaction.status === 'failed'
                                ? 'bg-red-50'
                                : 'bg-yellow-50'
                            }`}>
                              <Receipt className={`w-6 h-6 ${
                                transaction.status === 'paid'
                                  ? 'text-green-600'
                                  : transaction.status === 'failed'
                                  ? 'text-red-600'
                                  : 'text-yellow-600'
                              }`} />
                            </div>

                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-text-primary truncate">
                                {transaction.description}
                              </h3>
                              <div className="flex items-center gap-4 mt-1">
                                <p className="text-sm text-text-secondary">
                                  {formatDate(transaction.date)}
                                </p>
                                <p className="text-sm text-text-secondary">
                                  {transaction.paymentMethod}
                                </p>
                                <StatusBadge status={transaction.status} />
                              </div>
                            </div>
                          </div>

                          {/* Right side - Amount and actions */}
                          <div className="flex items-center gap-4">
                            <div className="text-right">
                              <p className={`font-semibold ${
                                transaction.status === 'paid'
                                  ? 'text-text-primary'
                                  : 'text-text-secondary'
                              }`}>
                                {formatAmount(transaction.amount, transaction.currency)}
                              </p>
                              <p className="text-sm text-text-secondary">
                                {transaction.id}
                              </p>
                            </div>

                            {/* Action menu */}
                            <div className="dropdown dropdown-end">
                              <button tabIndex={0} className="btn btn-ghost btn-sm btn-circle">
                                <MoreHorizontal className="w-4 h-4" />
                              </button>
                              <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                <li>
                                  <a href={transaction.invoiceUrl} className="gap-2">
                                    <Download className="w-4 h-4" />
                                    Download Invoice
                                  </a>
                                </li>
                                <li>
                                  <a href={transaction.receiptUrl} className="gap-2">
                                    <Receipt className="w-4 h-4" />
                                    View Receipt
                                  </a>
                                </li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </>
          )}
        </BillingHistoryClient>

        {/* Footer with help text */}
        <div className="bg-section-bg-accent rounded-xl p-6 border border-accent-bg-light">
          <div className="flex items-start gap-4">
            <div className="w-10 h-10 bg-link-default/10 rounded-lg flex items-center justify-center flex-shrink-0">
              <AlertCircle className="w-5 h-5 text-link-default" />
            </div>
            <div>
              <h3 className="font-medium text-text-primary mb-1">Need help with billing?</h3>
              <p className="text-text-secondary text-sm mb-3">
                If you have questions about your billing or need to update payment methods, we&apos;re here to help.
              </p>
              <div className="flex gap-3">
                <button className="btn btn-outline btn-sm">
                  Contact Support
                </button>
                <button className="btn btn-link btn-sm p-0 min-h-0 h-auto">
                  Billing FAQ
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
}

// Main page component
export default function BillingHistoryPage() {
  return (
    <Suspense fallback={<BillingHistoryLoading />}>
      <BillingHistoryContent />
    </Suspense>
  );
} 